/*
Golden Sky Home - Custom Theme
Luxury Black & Gold Design
Font: Montserrat
*/

/* Override font family with fallbacks */
body, html {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
    font-display: swap;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
    font-weight: 600;
    font-display: swap;
}

/* Ensure consistent font loading */
.golden-sky-theme * {
    font-family: inherit;
}

/* Typography hierarchy */
.golden-sky-theme h1 { font-size: 2.5rem; font-weight: 700; }
.golden-sky-theme h2 { font-size: 2rem; font-weight: 600; }
.golden-sky-theme h3 { font-size: 1.5rem; font-weight: 600; }
.golden-sky-theme h4 { font-size: 1.25rem; font-weight: 500; }
.golden-sky-theme h5 { font-size: 1.1rem; font-weight: 500; }
.golden-sky-theme h6 { font-size: 1rem; font-weight: 500; }

/* Golden Sky Theme Colors */
.golden-sky-theme {
    --primary-gold: #D4AF37;
    --secondary-gold: #FFD700;
    --dark-gold: #B8860B;
    --luxury-black: #1a1a1a;
    --deep-black: #000000;
    --soft-white: #f8f8f8;
}

/* Background and main styling */
.golden-sky-theme .page-cover .cover-bg-mask {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.85) 0%, rgba(0, 0, 0, 0.75) 100%) !important;
}

.golden-sky-theme .page-cover {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
}

/* Timer section styling */
.golden-sky-theme .pane-when {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(26, 26, 26, 0.95) 100%);
    border: 2px solid #D4AF37;
}

.golden-sky-theme .clock-countdown .digit {
    color: #D4AF37;
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.golden-sky-theme .clock-countdown .digit .days {
    font-size: 4rem;
    font-weight: 800;
    color: #FFD700;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
}

.golden-sky-theme .clock-countdown .digit .txt {
    color: #D4AF37;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.golden-sky-theme .clock-countdown .elem-bottom span {
    color: #D4AF37;
    font-weight: 600;
}

.golden-sky-theme .clock-countdown .elem-bottom .thin {
    color: #FFD700;
    font-weight: 300;
}

.golden-sky-theme .pane-when footer p {
    color: #f8f8f8;
    font-weight: 400;
}

.golden-sky-theme .pane-when footer .opening-date {
    color: #D4AF37;
    font-weight: 600;
    font-size: 1.1rem;
    margin-top: 10px;
}

/* Header styling */
.golden-sky-theme .page-home .header h2 {
    color: #f8f8f8;
    font-weight: 300;
}

.golden-sky-theme .page-home .header h2 strong {
    color: #D4AF37;
    font-weight: 700;
}

.golden-sky-theme .page-home .header h3 {
    color: #FFD700;
    font-weight: 500;
}

.golden-sky-theme .page-home .header .subhead a {
    color: #D4AF37;
    border: 2px solid #D4AF37;
    padding: 10px 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.golden-sky-theme .page-home .header .subhead a:hover {
    background: #D4AF37;
    color: #1a1a1a;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

/* Navigation styling */
.golden-sky-theme .quick-link {
    background: rgba(26, 26, 26, 0.95);
    border-right: 3px solid #D4AF37;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
}

.golden-sky-theme .quick-link .qmenu li {
    position: relative;
    overflow: hidden;
}

.golden-sky-theme .quick-link .qmenu li::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: left 0.5s ease;
}

.golden-sky-theme .quick-link .qmenu li:hover::before {
    left: 100%;
}

.golden-sky-theme .quick-link .qmenu li a {
    color: #D4AF37;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
}

.golden-sky-theme .quick-link .qmenu li a .icon {
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.golden-sky-theme .quick-link .qmenu li a:hover,
.golden-sky-theme .quick-link .qmenu li.active a {
    color: #FFD700;
    background: rgba(212, 175, 55, 0.15);
    transform: scale(1.05);
}

.golden-sky-theme .quick-link .qmenu li a:hover .icon {
    transform: scale(1.2);
}

.golden-sky-theme .quick-link .title {
    color: #f8f8f8;
    font-weight: 400;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.golden-sky-theme .quick-link .qmenu li:hover .title {
    color: #FFD700;
}

/* Form styling */
.golden-sky-theme .form .input input {
    background: rgba(26, 26, 26, 0.8);
    border: 2px solid #D4AF37;
    color: #f8f8f8;
    font-family: 'Montserrat', sans-serif;
}

.golden-sky-theme .form .input input:focus {
    border-color: #FFD700;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.golden-sky-theme .form .button {
    background: linear-gradient(135deg, #D4AF37 0%, #FFD700 100%);
    color: #1a1a1a;
    border: none;
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    transition: all 0.3s ease;
}

.golden-sky-theme .form .button:hover {
    background: linear-gradient(135deg, #FFD700 0%, #D4AF37 100%);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    transform: translateY(-2px);
}

/* Page titles */
.golden-sky-theme .p-title h3 {
    color: #D4AF37;
    font-weight: 600;
}

.golden-sky-theme .p-title .subhead {
    color: #f8f8f8;
    font-weight: 400;
}

.golden-sky-theme .p-title .subhead .bold {
    color: #FFD700;
    font-weight: 700;
}

/* Contact section */
.golden-sky-theme .contact h4 {
    color: #D4AF37;
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.golden-sky-theme .contact p {
    color: #f8f8f8;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 8px;
}

.golden-sky-theme .contact a {
    color: #FFD700;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
}

.golden-sky-theme .contact a:hover {
    color: #D4AF37;
    border-bottom-color: #D4AF37;
}

/* Contact icons */
.golden-sky-theme .contact-icon {
    color: #D4AF37;
    font-size: 1.3rem;
    margin-right: 12px;
    min-width: 24px;
    text-align: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
    display: inline-block;
    vertical-align: middle;
}

.golden-sky-theme .contact-icon.ion-email {
    font-size: 1.2rem;
}

.golden-sky-theme .contact-icon.ion-ios-location {
    font-size: 1.4rem;
    color: #FFD700;
}

.golden-sky-theme .contact-icon.ion-ios-telephone {
    font-size: 1.3rem;
    color: #D4AF37;
}

.golden-sky-theme .contact li:hover .contact-icon {
    color: #FFD700;
    transform: scale(1.1);
}

/* Contact list styling */
.golden-sky-theme .contact ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.golden-sky-theme .contact li {
    margin-bottom: 25px;
    padding: 18px;
    background: rgba(26, 26, 26, 0.4);
    border-radius: 10px;
    border-left: 4px solid rgba(212, 175, 55, 0.6);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.golden-sky-theme .contact li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.golden-sky-theme .contact li:hover::before {
    opacity: 1;
}

.golden-sky-theme .contact li:hover {
    background: rgba(26, 26, 26, 0.6);
    border-left-color: #FFD700;
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.15);
}

/* Specific styling for each contact type */
.golden-sky-theme .contact li:nth-child(1) {
    border-left-color: rgba(255, 215, 0, 0.6); /* Email - Gold */
}

.golden-sky-theme .contact li:nth-child(2) {
    border-left-color: rgba(212, 175, 55, 0.6); /* Address - Primary Gold */
}

.golden-sky-theme .contact li:nth-child(3) {
    border-left-color: rgba(184, 134, 11, 0.6); /* Phone - Dark Gold */
}

.golden-sky-theme .contact li:nth-child(1):hover {
    border-left-color: #FFD700;
}

.golden-sky-theme .contact li:nth-child(2):hover {
    border-left-color: #D4AF37;
}

.golden-sky-theme .contact li:nth-child(3):hover {
    border-left-color: #B8860B;
}

/* Timer section icons */
.golden-sky-theme .timer-icon {
    color: #FFD700;
    font-size: 1.2rem;
    margin-right: 8px;
    vertical-align: middle;
}

/* Social links */
.golden-sky-theme .socialnet a {
    color: #D4AF37;
    font-size: 1.5rem;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.golden-sky-theme .socialnet a:hover {
    color: #FFD700;
    transform: scale(1.2);
}

/* Scroll down button */
.golden-sky-theme .p-scrolldown .arrow-d {
    border: 2px solid #D4AF37;
    color: #D4AF37;
}

.golden-sky-theme .p-scrolldown .arrow-d:hover {
    background: #D4AF37;
    color: #1a1a1a;
}

/* Logo styling */
.golden-sky-theme .logo img {
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.3));
}

/* Text content */
.golden-sky-theme .text p {
    color: #f8f8f8;
    font-weight: 400;
    line-height: 1.6;
    margin-bottom: 15px;
}

.golden-sky-theme .text strong {
    color: #D4AF37;
    font-weight: 600;
}

/* About section specific styling */
.golden-sky-theme .page-about .text {
    max-width: 1000px;
    margin: 0 auto;
    text-align: left;
}

.golden-sky-theme .page-about .text p {
    font-size: 1.1rem;
    margin-bottom: 18px;
    line-height: 1.7;
}

/* Intro section redesign */
.golden-sky-theme .intro-section {
    margin-bottom: 50px;
}

.golden-sky-theme .intro-hero {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(212, 175, 55, 0.1) 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.golden-sky-theme .intro-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.golden-sky-theme .intro-item {
    display: flex;
    align-items: flex-start;
    position: relative;
    z-index: 1;
}

.golden-sky-theme .intro-icon-wrapper {
    background: linear-gradient(135deg, #D4AF37 0%, #FFD700 100%);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25px;
    flex-shrink: 0;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.golden-sky-theme .intro-icon {
    color: #1a1a1a;
    font-size: 1.8rem;
    font-weight: bold;
}

.golden-sky-theme .intro-content {
    flex: 1;
}

.golden-sky-theme .intro-title {
    color: #FFD700;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.golden-sky-theme .intro-content p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.6;
}

.golden-sky-theme .intro-description {
    text-align: center;
    padding: 0 20px;
}

.golden-sky-theme .lead-text {
    font-size: 1.2rem;
    font-weight: 400;
    color: #f8f8f8;
    line-height: 1.8;
    font-style: italic;
    margin: 0;
}

/* Services section */
.golden-sky-theme .services-section {
    margin: 50px 0;
}

.golden-sky-theme .services-title {
    text-align: center;
    color: #D4AF37;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 40px;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.golden-sky-theme .services-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #D4AF37, #FFD700, #D4AF37);
    border-radius: 2px;
}

/* Services grid */
.golden-sky-theme .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.golden-sky-theme .service-item {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(212, 175, 55, 0.05) 100%);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 15px;
    padding: 30px;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.golden-sky-theme .service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #D4AF37, #FFD700, #D4AF37);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.golden-sky-theme .service-item:hover::before {
    transform: scaleX(1);
}

.golden-sky-theme .service-item:hover {
    border-color: #FFD700;
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(212, 175, 55, 0.1) 100%);
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
}

.golden-sky-theme .service-icon-wrapper {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
    border: 2px solid rgba(212, 175, 55, 0.5);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
    transition: all 0.3s ease;
}

.golden-sky-theme .service-item:hover .service-icon-wrapper {
    background: linear-gradient(135deg, #D4AF37 0%, #FFD700 100%);
    border-color: #FFD700;
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
}

/* Service icons - unified styling */
.golden-sky-theme .service-icon {
    color: #D4AF37;
    font-size: 2.2rem;
    transition: all 0.3s ease;
}

.golden-sky-theme .service-item:hover .service-icon {
    color: #1a1a1a;
    transform: scale(1.1);
}

.golden-sky-theme .service-content {
    flex: 1;
}

.golden-sky-theme .service-title {
    color: #FFD700;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 15px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.golden-sky-theme .service-content p {
    margin: 0;
    line-height: 1.6;
    color: #f8f8f8;
    font-size: 1rem;
}

/* Signature section */
.golden-sky-theme .signature-section {
    margin-top: 50px;
    text-align: center;
}

.golden-sky-theme .signature-wrapper {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(212, 175, 55, 0.1) 100%);
    border: 2px solid rgba(212, 175, 55, 0.4);
    border-radius: 20px;
    padding: 40px;
    display: inline-flex;
    align-items: center;
    gap: 25px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.golden-sky-theme .signature-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.golden-sky-theme .signature-icon {
    color: #FFD700;
    font-size: 3rem;
    animation: goldGlow 3s ease-in-out infinite;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.golden-sky-theme .signature-content {
    text-align: left;
    position: relative;
    z-index: 1;
}

.golden-sky-theme .signature-title {
    color: #D4AF37;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.golden-sky-theme .signature-text {
    color: #FFD700;
    font-size: 1.1rem;
    font-style: italic;
    margin: 0;
    line-height: 1.5;
    font-weight: 400;
}

/* Page loader */
.golden-sky-theme .page-loader {
    background: #1a1a1a;
}

.golden-sky-theme .page-loader i {
    color: #D4AF37;
}

.golden-sky-theme .page-loader p {
    color: #f8f8f8;
    font-family: 'Montserrat', sans-serif;
}

/* Menu header */
.golden-sky-theme .header-top {
    background: rgba(26, 26, 26, 0.95);
    border-bottom: 2px solid #D4AF37;
}

.golden-sky-theme .header-top .menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.golden-sky-theme .header-top .menu a {
    color: #D4AF37;
    font-weight: 500;
    transition: color 0.3s ease;
    text-decoration: none;
    padding: 10px 15px;
}

.golden-sky-theme .header-top .menu a:hover {
    color: #FFD700;
}

/* Language switcher */
.golden-sky-theme .language-switcher {
    display: flex;
    align-items: center;
    gap: 5px;
}

.golden-sky-theme .language-switcher .lang-btn {
    color: #D4AF37 !important;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 5px 10px !important;
    border: 1px solid transparent;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.golden-sky-theme .language-switcher .lang-btn.active {
    color: #1a1a1a !important;
    background: #D4AF37;
    border-color: #D4AF37;
}

.golden-sky-theme .language-switcher .lang-btn:hover {
    color: #FFD700 !important;
    border-color: #FFD700;
}

.golden-sky-theme .language-switcher .lang-separator {
    color: #D4AF37;
    font-weight: 300;
}

/* Cross-browser compatibility */
.golden-sky-theme .service-item {
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.golden-sky-theme .pane-when {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.golden-sky-theme .quick-link {
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
}

.golden-sky-theme .header-top {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* Animation effects */
@keyframes goldGlow {
    0% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
    50% { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
    100% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
}

@-webkit-keyframes goldGlow {
    0% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
    50% { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
    100% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
}

.golden-sky-theme .clock-countdown .digit .days {
    -webkit-animation: goldGlow 2s ease-in-out infinite;
    animation: goldGlow 2s ease-in-out infinite;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus states for accessibility */
.golden-sky-theme .form .button:focus,
.golden-sky-theme .lang-btn:focus,
.golden-sky-theme .quick-link a:focus {
    outline: 2px solid #D4AF37;
    outline-offset: 2px;
}

/* Luxury enhancements */
.golden-sky-theme .page-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.golden-sky-theme .pane-when {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.golden-sky-theme .quick-link {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.golden-sky-theme .header-top {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Enhanced hover effects */
.golden-sky-theme .form .button {
    position: relative;
    overflow: hidden;
}

.golden-sky-theme .form .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.golden-sky-theme .form .button:hover::before {
    left: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .golden-sky-theme .clock-countdown .digit .days {
        font-size: 3rem;
    }

    .golden-sky-theme .page-home .header h2 {
        font-size: 1.8rem;
    }

    .golden-sky-theme .page-home .header h3 {
        font-size: 1.4rem;
    }

    .golden-sky-theme .header-top .menu {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .golden-sky-theme .language-switcher {
        margin-top: 10px;
    }

    /* Services responsive */
    .golden-sky-theme .services-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin: 30px 0;
    }

    .golden-sky-theme .services-title {
        font-size: 1.5rem;
        margin-bottom: 30px;
    }

    .golden-sky-theme .service-item {
        padding: 25px;
    }

    .golden-sky-theme .service-icon-wrapper {
        width: 70px;
        height: 70px;
        margin-bottom: 15px;
    }

    .golden-sky-theme .service-icon {
        font-size: 2rem;
    }

    .golden-sky-theme .service-title {
        font-size: 1.1rem;
    }

    /* Contact responsive */
    .golden-sky-theme .contact-icon {
        font-size: 1.1rem;
    }

    .golden-sky-theme .contact-icon.ion-ios-location {
        font-size: 1.2rem;
    }

    .golden-sky-theme .contact li {
        padding: 15px;
        margin-bottom: 20px;
        transform: none !important; /* Disable transform on mobile */
    }

    .golden-sky-theme .contact li:hover {
        transform: none !important;
    }

    /* Intro section responsive */
    .golden-sky-theme .intro-hero {
        padding: 25px;
        margin-bottom: 25px;
    }

    .golden-sky-theme .intro-item {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .golden-sky-theme .intro-icon-wrapper {
        width: 50px;
        height: 50px;
        margin-right: 0;
    }

    .golden-sky-theme .intro-icon {
        font-size: 1.5rem;
    }

    .golden-sky-theme .intro-title {
        font-size: 1.1rem;
    }

    .golden-sky-theme .intro-content {
        text-align: center;
    }

    .golden-sky-theme .lead-text {
        font-size: 1.1rem;
    }

    /* Signature responsive */
    .golden-sky-theme .signature-wrapper {
        flex-direction: column;
        padding: 30px;
        gap: 20px;
    }

    .golden-sky-theme .signature-icon {
        font-size: 2.5rem;
    }

    .golden-sky-theme .signature-content {
        text-align: center;
    }

    .golden-sky-theme .signature-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .golden-sky-theme .clock-countdown .digit .days {
        font-size: 2.5rem;
    }

    .golden-sky-theme .page-home .header h2 {
        font-size: 1.5rem;
    }

    .golden-sky-theme .page-home .header h3 {
        font-size: 1.2rem;
    }

    /* Typography adjustments */
    .golden-sky-theme h1 { font-size: 2rem; }
    .golden-sky-theme h2 { font-size: 1.5rem; }
    .golden-sky-theme h3 { font-size: 1.25rem; }
    .golden-sky-theme h4 { font-size: 1.1rem; }

    /* Service items mobile */
    .golden-sky-theme .services-title {
        font-size: 1.3rem;
        margin-bottom: 25px;
    }

    .golden-sky-theme .service-item {
        padding: 20px;
    }

    .golden-sky-theme .service-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .golden-sky-theme .service-icon {
        font-size: 1.8rem;
    }

    .golden-sky-theme .service-title {
        font-size: 1rem;
    }

    .golden-sky-theme .service-content p {
        font-size: 0.95rem;
    }

    /* Contact mobile */
    .golden-sky-theme .contact h4 {
        font-size: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .golden-sky-theme .contact-icon {
        font-size: 1rem;
        margin-right: 8px;
        min-width: 20px;
    }

    .golden-sky-theme .contact-icon.ion-email {
        font-size: 0.9rem;
    }

    .golden-sky-theme .contact-icon.ion-ios-location {
        font-size: 1.1rem;
    }

    .golden-sky-theme .contact-icon.ion-ios-telephone {
        font-size: 1rem;
    }

    .golden-sky-theme .contact li {
        padding: 10px;
        margin-bottom: 15px;
    }

    /* Intro section mobile */
    .golden-sky-theme .intro-hero {
        padding: 20px;
    }

    .golden-sky-theme .intro-icon-wrapper {
        width: 45px;
        height: 45px;
    }

    .golden-sky-theme .intro-icon {
        font-size: 1.3rem;
    }

    .golden-sky-theme .intro-title {
        font-size: 1rem;
    }

    .golden-sky-theme .intro-content p {
        font-size: 1rem;
    }

    .golden-sky-theme .lead-text {
        font-size: 1rem;
    }

    /* Signature mobile */
    .golden-sky-theme .signature-wrapper {
        padding: 25px;
    }

    .golden-sky-theme .signature-icon {
        font-size: 2rem;
    }

    .golden-sky-theme .signature-title {
        font-size: 1.3rem;
    }

    .golden-sky-theme .signature-text {
        font-size: 1rem;
    }

    /* Page about text */
    .golden-sky-theme .page-about .text p {
        font-size: 1rem;
    }
}
