/*
Golden Sky Home - Custom Theme
Luxury Black & Gold Design
Font: Montserrat
*/

/* Override font family */
body, html {
    font-family: 'Montserrat', sans-serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 600;
}

/* Golden Sky Theme Colors */
.golden-sky-theme {
    --primary-gold: #D4AF37;
    --secondary-gold: #FFD700;
    --dark-gold: #B8860B;
    --luxury-black: #1a1a1a;
    --deep-black: #000000;
    --soft-white: #f8f8f8;
}

/* Background and main styling */
.golden-sky-theme .page-cover .cover-bg-mask {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.85) 0%, rgba(0, 0, 0, 0.75) 100%) !important;
}

.golden-sky-theme .page-cover {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
}

/* Timer section styling */
.golden-sky-theme .pane-when {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(26, 26, 26, 0.95) 100%);
    border: 2px solid #D4AF37;
}

.golden-sky-theme .clock-countdown .digit {
    color: #D4AF37;
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.golden-sky-theme .clock-countdown .digit .days {
    font-size: 4rem;
    font-weight: 800;
    color: #FFD700;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
}

.golden-sky-theme .clock-countdown .digit .txt {
    color: #D4AF37;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.golden-sky-theme .clock-countdown .elem-bottom span {
    color: #D4AF37;
    font-weight: 600;
}

.golden-sky-theme .clock-countdown .elem-bottom .thin {
    color: #FFD700;
    font-weight: 300;
}

.golden-sky-theme .pane-when footer p {
    color: #f8f8f8;
    font-weight: 400;
}

.golden-sky-theme .pane-when footer .opening-date {
    color: #D4AF37;
    font-weight: 600;
    font-size: 1.1rem;
    margin-top: 10px;
}

/* Header styling */
.golden-sky-theme .page-home .header h2 {
    color: #f8f8f8;
    font-weight: 300;
}

.golden-sky-theme .page-home .header h2 strong {
    color: #D4AF37;
    font-weight: 700;
}

.golden-sky-theme .page-home .header h3 {
    color: #FFD700;
    font-weight: 500;
}

.golden-sky-theme .page-home .header .subhead a {
    color: #D4AF37;
    border: 2px solid #D4AF37;
    padding: 10px 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.golden-sky-theme .page-home .header .subhead a:hover {
    background: #D4AF37;
    color: #1a1a1a;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

/* Navigation styling */
.golden-sky-theme .quick-link {
    background: rgba(26, 26, 26, 0.9);
    border-right: 3px solid #D4AF37;
}

.golden-sky-theme .quick-link .qmenu li a {
    color: #D4AF37;
    transition: all 0.3s ease;
}

.golden-sky-theme .quick-link .qmenu li a:hover,
.golden-sky-theme .quick-link .qmenu li.active a {
    color: #FFD700;
    background: rgba(212, 175, 55, 0.1);
}

.golden-sky-theme .quick-link .title {
    color: #f8f8f8;
    font-weight: 400;
}

/* Form styling */
.golden-sky-theme .form .input input {
    background: rgba(26, 26, 26, 0.8);
    border: 2px solid #D4AF37;
    color: #f8f8f8;
    font-family: 'Montserrat', sans-serif;
}

.golden-sky-theme .form .input input:focus {
    border-color: #FFD700;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.golden-sky-theme .form .button {
    background: linear-gradient(135deg, #D4AF37 0%, #FFD700 100%);
    color: #1a1a1a;
    border: none;
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    transition: all 0.3s ease;
}

.golden-sky-theme .form .button:hover {
    background: linear-gradient(135deg, #FFD700 0%, #D4AF37 100%);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    transform: translateY(-2px);
}

/* Page titles */
.golden-sky-theme .p-title h3 {
    color: #D4AF37;
    font-weight: 600;
}

.golden-sky-theme .p-title .subhead {
    color: #f8f8f8;
    font-weight: 400;
}

.golden-sky-theme .p-title .subhead .bold {
    color: #FFD700;
    font-weight: 700;
}

/* Contact section */
.golden-sky-theme .contact h4 {
    color: #D4AF37;
    font-weight: 600;
}

.golden-sky-theme .contact p {
    color: #f8f8f8;
    font-weight: 400;
}

.golden-sky-theme .contact a {
    color: #FFD700;
    text-decoration: none;
    transition: color 0.3s ease;
}

.golden-sky-theme .contact a:hover {
    color: #D4AF37;
}

/* Social links */
.golden-sky-theme .socialnet a {
    color: #D4AF37;
    font-size: 1.5rem;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.golden-sky-theme .socialnet a:hover {
    color: #FFD700;
    transform: scale(1.2);
}

/* Scroll down button */
.golden-sky-theme .p-scrolldown .arrow-d {
    border: 2px solid #D4AF37;
    color: #D4AF37;
}

.golden-sky-theme .p-scrolldown .arrow-d:hover {
    background: #D4AF37;
    color: #1a1a1a;
}

/* Logo styling */
.golden-sky-theme .logo img {
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.3));
}

/* Text content */
.golden-sky-theme .text p {
    color: #f8f8f8;
    font-weight: 400;
    line-height: 1.6;
}

.golden-sky-theme .text strong {
    color: #D4AF37;
    font-weight: 600;
}

/* Page loader */
.golden-sky-theme .page-loader {
    background: #1a1a1a;
}

.golden-sky-theme .page-loader i {
    color: #D4AF37;
}

.golden-sky-theme .page-loader p {
    color: #f8f8f8;
    font-family: 'Montserrat', sans-serif;
}

/* Menu header */
.golden-sky-theme .header-top {
    background: rgba(26, 26, 26, 0.95);
    border-bottom: 2px solid #D4AF37;
}

.golden-sky-theme .header-top .menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.golden-sky-theme .header-top .menu a {
    color: #D4AF37;
    font-weight: 500;
    transition: color 0.3s ease;
    text-decoration: none;
    padding: 10px 15px;
}

.golden-sky-theme .header-top .menu a:hover {
    color: #FFD700;
}

/* Language switcher */
.golden-sky-theme .language-switcher {
    display: flex;
    align-items: center;
    gap: 5px;
}

.golden-sky-theme .language-switcher .lang-btn {
    color: #D4AF37 !important;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 5px 10px !important;
    border: 1px solid transparent;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.golden-sky-theme .language-switcher .lang-btn.active {
    color: #1a1a1a !important;
    background: #D4AF37;
    border-color: #D4AF37;
}

.golden-sky-theme .language-switcher .lang-btn:hover {
    color: #FFD700 !important;
    border-color: #FFD700;
}

.golden-sky-theme .language-switcher .lang-separator {
    color: #D4AF37;
    font-weight: 300;
}

/* Animation effects */
@keyframes goldGlow {
    0% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
    50% { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
    100% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.5); }
}

.golden-sky-theme .clock-countdown .digit .days {
    animation: goldGlow 2s ease-in-out infinite;
}

/* Luxury enhancements */
.golden-sky-theme .page-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.golden-sky-theme .pane-when {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.golden-sky-theme .quick-link {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.golden-sky-theme .header-top {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Enhanced hover effects */
.golden-sky-theme .form .button {
    position: relative;
    overflow: hidden;
}

.golden-sky-theme .form .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.golden-sky-theme .form .button:hover::before {
    left: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .golden-sky-theme .clock-countdown .digit .days {
        font-size: 3rem;
    }

    .golden-sky-theme .page-home .header h2 {
        font-size: 1.8rem;
    }

    .golden-sky-theme .page-home .header h3 {
        font-size: 1.4rem;
    }

    .golden-sky-theme .header-top .menu {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .golden-sky-theme .language-switcher {
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .golden-sky-theme .clock-countdown .digit .days {
        font-size: 2.5rem;
    }

    .golden-sky-theme .page-home .header h2 {
        font-size: 1.5rem;
    }

    .golden-sky-theme .page-home .header h3 {
        font-size: 1.2rem;
    }
}
