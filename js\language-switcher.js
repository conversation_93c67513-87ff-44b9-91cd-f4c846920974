/**
 * Golden Sky Home - Language Switcher
 * Français / English
 */

// Translations object
const translations = {
    fr: {
        // Timer section
        'days': 'jours',
        'timer-text': 'Avant l\'ouverture de notre <strong>Appart Hôtel Golden Sky Home</strong>',
        'opening-date': 'Ouverture le <strong>15 août 2025 à 10h00</strong>',
        
        // Home section
        'home-title': 'Appart Hôtel <strong>Golden Sky Home</strong>',
        'home-subtitle': 'Café & <br>Restaurant',
        'home-cta': 'Bientôt disponible',
        
        // Registration section
        'register-title': 'Réservation',
        'register-subtitle': 'Inscrivez-vous pour recevoir nos dernières nouvelles',
        'register-invite': 'Veuillez saisir votre email ci-dessous pour rester en contact avec nous :',
        'register-placeholder': '<EMAIL>',
        'register-button': 'S\'inscrire',
        'register-success': '<strong>Merci</strong> pour votre inscription. Nous vous tiendrons informé.',
        
        // About section
        'about-title': 'À Propos',
        'about-subtitle': 'Nous créons des <span class="bold">expériences</span> <span class="bold">exceptionnelles</span>',
        'about-text1': 'L\'<strong>Appart Hôtel Golden Sky Home</strong> vous accueillera bientôt dans un cadre élégant et moderne au cœur de Béni Mellal. Notre établissement combine le confort d\'un appartement avec les services d\'un hôtel de qualité.',
        'about-text2': 'Découvrez notre café-restaurant où vous pourrez savourer une cuisine raffinée dans une atmosphère chaleureuse et conviviale. Une expérience unique vous attend.',
        
        // Contact section
        'contact-title': 'Contact',
        'contact-address': 'Adresse',
        'contact-phone': 'Téléphone',
        'contact-languages': 'Langues',
        'contact-follow': 'Suivez-nous',
        'contact-copyright': '© 2025 <strong>Appart Hôtel Golden Sky Home</strong>. Tous droits réservés.',
        
        // Navigation
        'nav-home': 'Accueil',
        'nav-register': 'Réservation',
        'nav-about': 'À propos',
        'nav-contact': 'Contact',
        'menu-about': 'à propos',
        'menu-contact': 'contact',
        
        // Scroll buttons
        'scroll-down': 'Défiler',
        'scroll-about': 'À propos',
        'scroll-contact': 'Contact',
        'scroll-message': 'Message'
    },
    en: {
        // Timer section
        'days': 'days',
        'timer-text': 'Before the opening of our <strong>Golden Sky Home Apart Hotel</strong>',
        'opening-date': 'Opening on <strong>August 15, 2025 at 10:00 AM</strong>',
        
        // Home section
        'home-title': 'Apart Hotel <strong>Golden Sky Home</strong>',
        'home-subtitle': 'Café & <br>Restaurant',
        'home-cta': 'Available soon',
        
        // Registration section
        'register-title': 'Reservation',
        'register-subtitle': 'Register to get our latest news',
        'register-invite': 'Please enter your email below to stay in touch with us:',
        'register-placeholder': '<EMAIL>',
        'register-button': 'Subscribe',
        'register-success': '<strong>Thank you</strong> for your subscription. We will keep you informed.',
        
        // About section
        'about-title': 'About Us',
        'about-subtitle': 'We create <span class="bold">exceptional</span> <span class="bold">experiences</span>',
        'about-text1': 'The <strong>Golden Sky Home Apart Hotel</strong> will soon welcome you in an elegant and modern setting in the heart of Béni Mellal. Our establishment combines the comfort of an apartment with quality hotel services.',
        'about-text2': 'Discover our café-restaurant where you can enjoy refined cuisine in a warm and friendly atmosphere. A unique experience awaits you.',
        
        // Contact section
        'contact-title': 'Contact',
        'contact-address': 'Address',
        'contact-phone': 'Phone',
        'contact-languages': 'Languages',
        'contact-follow': 'Follow us',
        'contact-copyright': '© 2025 <strong>Golden Sky Home Apart Hotel</strong>. All rights reserved.',
        
        // Navigation
        'nav-home': 'Home',
        'nav-register': 'Reservation',
        'nav-about': 'About',
        'nav-contact': 'Contact',
        'menu-about': 'about',
        'menu-contact': 'contact',
        
        // Scroll buttons
        'scroll-down': 'Scroll',
        'scroll-about': 'About',
        'scroll-contact': 'Contact',
        'scroll-message': 'Message'
    }
};

// Current language
let currentLang = 'fr';

// Initialize language switcher
function initLanguageSwitcher() {
    const langButtons = document.querySelectorAll('.lang-btn');
    
    langButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const lang = this.id.split('-')[1]; // Extract 'fr' or 'en' from 'lang-fr' or 'lang-en'
            switchLanguage(lang);
        });
    });
    
    // Set initial language
    switchLanguage(currentLang);
}

// Switch language function
function switchLanguage(lang) {
    if (!translations[lang]) return;
    
    currentLang = lang;
    
    // Update active button
    document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(`lang-${lang}`).classList.add('active');
    
    // Update HTML lang attribute
    document.documentElement.lang = lang;
    
    // Update all translatable elements
    updateTranslations(lang);
}

// Update translations in the DOM
function updateTranslations(lang) {
    const t = translations[lang];
    
    // Timer section
    const daysText = document.querySelector('.clock-countdown .txt');
    if (daysText) daysText.textContent = t['days'];
    
    const timerText = document.querySelector('.pane-when footer p:first-child');
    if (timerText) timerText.innerHTML = t['timer-text'];
    
    const openingDate = document.querySelector('.opening-date');
    if (openingDate) openingDate.innerHTML = t['opening-date'];
    
    // Home section
    const homeTitle = document.querySelector('.page-home .h-left h2');
    if (homeTitle) homeTitle.innerHTML = t['home-title'];
    
    const homeSubtitle = document.querySelector('.page-home .h-right h3');
    if (homeSubtitle) homeSubtitle.innerHTML = t['home-subtitle'];
    
    const homeCta = document.querySelector('.page-home .subhead a');
    if (homeCta) homeCta.textContent = t['home-cta'];
    
    // Registration section
    const registerTitle = document.querySelector('.page-register .p-title h3');
    if (registerTitle) registerTitle.innerHTML = t['register-title'] + ' <i class="ion ion-compose"></i>';
    
    const registerSubtitle = document.querySelector('.page-register .subhead');
    if (registerSubtitle) registerSubtitle.textContent = t['register-subtitle'];
    
    const registerInvite = document.querySelector('.page-register .invite');
    if (registerInvite) registerInvite.textContent = t['register-invite'];
    
    const registerInput = document.querySelector('#reg-email');
    if (registerInput) registerInput.placeholder = t['register-placeholder'];
    
    const registerButton = document.querySelector('#submit-email');
    if (registerButton) registerButton.textContent = t['register-button'];
    
    const registerSuccess = document.querySelector('.email-ok');
    if (registerSuccess) registerSuccess.innerHTML = t['register-success'];
    
    // About section
    const aboutTitle = document.querySelector('.page-about .p-title h3');
    if (aboutTitle) aboutTitle.innerHTML = t['about-title'] + '<i class="ion ion-android-information"></i>';
    
    const aboutSubtitle = document.querySelector('.page-about .subhead');
    if (aboutSubtitle) aboutSubtitle.innerHTML = t['about-subtitle'];
    
    const aboutTexts = document.querySelectorAll('.page-about .text p');
    if (aboutTexts[0]) aboutTexts[0].innerHTML = t['about-text1'];
    if (aboutTexts[1]) aboutTexts[1].innerHTML = t['about-text2'];
    
    // Contact section
    const contactHeaders = document.querySelectorAll('.contact h4');
    if (contactHeaders[1]) contactHeaders[1].textContent = t['contact-address'];
    if (contactHeaders[2]) contactHeaders[2].textContent = t['contact-phone'];
    
    const languagesHeader = document.querySelector('.contact .medium-6.right h4');
    if (languagesHeader) languagesHeader.textContent = t['contact-languages'];
    
    const followHeader = document.querySelector('.contact .medium-6.right ul li:nth-child(2) h4');
    if (followHeader) followHeader.textContent = t['contact-follow'];
    
    const copyright = document.querySelector('.contact .small');
    if (copyright) copyright.innerHTML = t['contact-copyright'];
    
    // Navigation
    const navTitles = document.querySelectorAll('.qmenu .title');
    if (navTitles[0]) navTitles[0].textContent = t['nav-home'];
    if (navTitles[1]) navTitles[1].textContent = t['nav-register'];
    if (navTitles[2]) navTitles[2].textContent = t['nav-about'];
    if (navTitles[3]) navTitles[3].textContent = t['nav-contact'];
    
    // Menu links
    const menuLinks = document.querySelectorAll('.header-top .menu a');
    if (menuLinks[0]) menuLinks[0].textContent = t['menu-about'];
    if (menuLinks[1]) menuLinks[1].textContent = t['menu-contact'];
    
    // Scroll buttons
    const scrollButtons = document.querySelectorAll('.arrow-d .before');
    scrollButtons.forEach((btn, index) => {
        switch(index) {
            case 0: btn.textContent = t['scroll-down']; break;
            case 1: btn.textContent = t['scroll-about']; break;
            case 2: btn.textContent = t['scroll-contact']; break;
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initLanguageSwitcher();
});
